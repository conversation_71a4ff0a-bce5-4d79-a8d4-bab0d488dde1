{"name": "beefsystem", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start --dev-client", "android": "DARK_MODE=media expo run:android", "ios": "DARK_MODE=media expo run:ios", "web": "DARK_MODE=media expo start --web"}, "dependencies": {"@gluestack-ui/button": "^1.0.14", "@gluestack-ui/form-control": "^0.1.19", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/image": "^0.1.17", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/toast": "^1.0.9", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.25", "babel-plugin-module-resolver": "^5.0.2", "expo": "~53.0.20", "expo-linear-gradient": "~14.1.5", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "^19.1.1", "react-native": "0.79.5", "react-native-css-interop": "^0.1.22", "react-native-reanimated": "^4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.1", "react-native-worklets": "0.4.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "jscodeshift": "^0.15.2"}, "private": true}