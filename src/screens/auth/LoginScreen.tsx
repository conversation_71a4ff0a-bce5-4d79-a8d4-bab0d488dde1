import { View, Text, StyleSheet, ImageBackground } from "react-native";
import Logo from '@/src/assets/logo.svg';
import BackgroundImg from '@/src/assets/login-background-img.jpg';
import { Input, InputField, InputIcon, InputSlot } from "@/components/ui/input"
import { FormControl, FormControlLabel, FormControlLabelText } from "@/components/ui/form-control";
import { EyeIcon, EyeOffIcon } from "@/components/ui/icon"
import { Button, ButtonText } from "@/components/ui/button";
import { VStack } from "@/components/ui/vstack";
import { LinearGradient } from "expo-linear-gradient";
import { useState } from "react";

export function LoginScreen() {
  const [showPassword, setShowPassword] = useState(false);
  const handleState = () => {
    setShowPassword((showState) => {
      return !showState
    })
  }

  return (
    <ImageBackground source={BackgroundImg} style={styles.container}>
      <LinearGradient
        colors={[
            "rgba(32, 48, 120, 0.6)",
            "rgba(32, 48, 120, 1)",
          ]}
        locations={[0, 0.7, 1]}
        style={StyleSheet.absoluteFill}
      />

      <VStack className="w-full h-full p-6 justify-center items-center gap-6">
        <Logo width={160} height={260} />
        <FormControl
          isInvalid={false}
          isDisabled={false}
          isReadOnly={false}
          isRequired={false}
        >
          <FormControlLabel>
            <FormControlLabelText className="text-lg font-semibold text-white">E-mail</FormControlLabelText>
          </FormControlLabel>
          <Input className="w-full bg-white rounded-xl h-14">
            <InputField className="text-lg px-4" />
          </Input>
          
          <FormControlLabel className="mt-4">
            <FormControlLabelText className="text-lg font-semibold text-white">Senha</FormControlLabelText>
          </FormControlLabel>
          <Input className="w-full bg-white rounded-xl h-14">
            <InputField type={showPassword ? 'text' : 'password'} className="text-lg px-4" />
            <InputSlot className="p-3" onPress={handleState}>
              <InputIcon className="text-primary-500" as={showPassword ? EyeIcon : EyeOffIcon} />
            </InputSlot>
          </Input>
        </FormControl>

        <Button className="w-full bg-primary-500 rounded-xl h-16 mt-6" size="lg">#2B409D
          <ButtonText className="text-white text-lg font-bold">Entrar</ButtonText>
        </Button>
        <Text className="text-white text-lg font-bold">Esqueceu sua senha?</Text>
      </VStack>
    </ImageBackground>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
